require('dotenv').config();
const config = require('./config.js');
const express = require('express');
const { ApolloServer } = require('apollo-server-express');
const { applyMiddleware } = require('graphql-middleware');
const mongoose = require('mongoose');
const engines = require('consolidate');
const typeDefs = require('./graphql/schema/index');
const resolvers = require('./graphql/resolvers/index');
const permissions = require('./graphql/permissions');
const paypal = require('./routes/paypal');
const stripe = require('./routes/stripe');
const session = require('express-session');
const isAuthenticated = require('./middleware/is-auth');
const { processWhatsAppAuth } = require('./middleware/whatsapp-graphql-auth');
const graphql = require('graphql');
const subscriptionTransportWs = require('subscriptions-transport-ws');
const graphqlTools = require('@graphql-tools/schema');
const logger = require('./helpers/logger');
const http = require('http');
const populateCountries = require('./helpers/populate-countries-data.js');
const restaurantStore = require('./whatsapp/services/restaurantStore');

const Sentry = require('@sentry/node');
const Tracing = require('@sentry/tracing');
const { SentryConfig } = require('./helpers/sentry.config.js');

/**
 * Fetch restaurants and brands data from GraphQL
 */
async function fetchRestaurantsAndBrandsData() {
  try {
    const Restaurant = require('./models/restaurant');
    const Brand = require('./models/brand');

    // 直接从数据库获取餐厅数据
    const restaurants = await Restaurant.find().lean();
    const brands = await Brand.find().lean();

    return {
      restaurants: restaurants,
      brands: brands
    };
  } catch (error) {
    logger.error('Failed to fetch restaurants and brands:', error);
    throw error;
  }
}

async function startApolloServer() {
  const app = express();
  const httpServer = http.createServer(app);

  // Ensure to call this before requiring any other modules!
  // initializing bug reporting platform i.e Sentry
  Sentry.init({
    dsn: config.SENTRY_DSN,
    debug: true,
    integrations: [
      new Sentry.Integrations.Http({ tracing: true }),
      new Tracing.Integrations.Express({ app, methods: ['get', 'post'] })
    ],
    tracesSampleRate: 1.0,
    profilesSampleRate: 1.0,
    environment: config.NODE_ENV
  });

  logger.info('Sentry initialized');

  // Connect to MongoDB first, before initializing any other services
  try {
    await mongoose.connect(config.CONNECTION_STRING, {
      dbName: config.DB_NAME
    });
    logger.info('Connected to MongoDB');
    logger.info('Loading countries data...');
    const populateCountries = require('./helpers/populate-countries-data');
    await populateCountries();

    // Initialize SessionService
    const sessionService = require('./whatsapp/services/sessionService');
    logger.info('Initializing SessionService...');
    await sessionService.initialize();
    logger.info('SessionService initialized successfully');

    // Initialize currency configuration
    const { initCurrency } = require('./whatsapp/machines/orderFsmActions');
    logger.info('Initializing currency configuration...');
    await initCurrency();
    logger.info('Currency configuration initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize MongoDB services:', error);
    throw error; // Stop server startup if any service fails
  }
  // Initialize restaurant store after MongoDB is connected
  try {
    await restaurantStore.initialize(fetchRestaurantsAndBrandsData);
    logger.info('Restaurant store initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize restaurant store:', error);
    // Continue starting the server even if restaurant store fails
    // It will attempt to load from backup if available
  }

  // Read CORS configuration from config file
  const corsConfig = config.CORS;
  const allowedOrigins = corsConfig.ALLOWED_ORIGINS.split(',');

  // Apply global CORS middleware to all routes
  app.use((req, res, next) => {
    const origin = req.headers.origin;

    // Allow if no origin (e.g., same-origin requests)
    if (!origin) {
      return next();
    }

    // Check if the origin matches allowed domains
    const isAllowed = allowedOrigins.some(allowedOrigin => {
      if (allowedOrigin.startsWith('.')) {
        // If config starts with a dot, match subdomains
        return origin.endsWith(allowedOrigin) || origin === 'https://' + allowedOrigin.substring(1);
      } else {
        // Otherwise exact match
        return origin === allowedOrigin;
      }
    });

    if (isAllowed) {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Expose-Headers', 'Content-Length, X-JSON');
    } else {
      // For other domains, allow access but without credentials
      res.setHeader('Access-Control-Allow-Origin', '*');
    }

    res.setHeader('Access-Control-Allow-Methods', corsConfig.ALLOWED_METHODS);
    res.setHeader('Access-Control-Allow-Headers', corsConfig.ALLOWED_HEADERS);

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }

    next();
  });

  // Set body parser - ensure it's applied before Apollo Server middleware
  app.use(express.json({ type: ['application/json', 'application/vnd.api+json'] }));
  app.use(express.urlencoded({ extended: true }));

  // Add WhatsApp API routes - ensure they're applied before Apollo Server middleware
  const whatsappRoutes = require('./whatsapp/routes/index');
  app.use('/whatsapp', whatsappRoutes);
  logger.info('WhatsApp API routes initialized');

  const executableSchema = graphqlTools.makeExecutableSchema({
    typeDefs,
    resolvers
  });

  // 将权限中间件应用到 schema
  const schemaWithPermissions = applyMiddleware(executableSchema, permissions);

  const server = new ApolloServer({
    schema: schemaWithPermissions,
    introspection: config.NODE_ENV === 'local',
    playground: config.NODE_ENV === 'local',
    context: async ({ req, res }) => {
      if (!req) return {};

      // 处理标准JWT认证
      const { isAuth, userId, userType, restaurantId } = isAuthenticated(req);
      req.isAuth = isAuth;
      req.userId = userId;
      req.userType = userType;
      req.restaurantId = restaurantId;

      // 创建基本上下文
      const context = { req, res };

      // 处理WhatsApp认证
      return await processWhatsAppAuth(context);
    },
    formatError: error => {
      // 检查是否是WhatsApp相关的错误
      if (error.extensions?.code?.startsWith('WHATSAPP_')) {
        logger.warn('WhatsApp authentication error:', error);

        // 返回友好的错误消息
        return {
          message: error.message,
          extensions: {
            code: error.extensions.code
          }
        };
      }

      // 处理其他错误
      logger.error('GraphQL error:', error);
      return {
        message: error.message,
        path: error.path,
        extensions: process.env.NODE_ENV === 'production' ? { code: error.extensions?.code } : error.extensions
      };
    },
    plugins: [SentryConfig]
  });
  const subscriptionServer = httpServer => {
    return subscriptionTransportWs.SubscriptionServer.create(
      {
        schema: schemaWithPermissions,
        execute: graphql.execute,
        subscribe: graphql.subscribe,
        onConnect() {
          logger.info('Connected to subscription server.');
        }
      },
      {
        server: httpServer,
        path: server.graphqlPath
      }
    );
  };
  await server.start();
  // Specify that Apollo Server only handles /graphql path requests
  // Use Apollo's built-in CORS functionality
  server.applyMiddleware({
    app,
    path: '/graphql',
    cors: {
      origin: function (origin, callback) {
        // Allow if no origin (e.g., same-origin requests)
        if (!origin) {
          return callback(null, true);
        }

        // Check if the origin matches allowed domains
        const isAllowed = allowedOrigins.some(allowedOrigin => {
          if (allowedOrigin.startsWith('.')) {
            // If config starts with a dot, match subdomains
            return origin.endsWith(allowedOrigin) || origin === 'https://' + allowedOrigin.substring(1);
          } else {
            // Otherwise exact match
            return origin === allowedOrigin;
          }
        });

        if (isAllowed) {
          callback(null, true);
        } else {
          // For other domains, allow access but without credentials
          callback(null, true);
        }
      },
      credentials: corsConfig.ALLOW_CREDENTIALS,
      methods: corsConfig.ALLOWED_METHODS.split(','),
      allowedHeaders: corsConfig.ALLOWED_HEADERS.split(',')
    }
  });
  app.engine('ejs', engines.ejs);
  app.set('views', './views');
  app.set('view engine', 'ejs');

  app.use(Sentry.Handlers.requestHandler());
  app.use(Sentry.Handlers.tracingHandler());
  app.use(Sentry.Handlers.errorHandler());

  // Session middleware for customer tracking
  app.use(
    session({
      secret: process.env.SESSION_SECRET || 'your-session-secret',
      resave: false,
      saveUninitialized: true,
      cookie: { secure: process.env.NODE_ENV === 'production' }
    })
  );

  app.use(express.static('public'));
  app.use('/sentry-crash', (req, res) => {
    throw new Error('Backend Crashed');
  });
  app.use('/paypal', paypal);
  app.use('/stripe', stripe);

  // Make sure to call listen on httpServer, NOT on app.
  await new Promise(resolve => httpServer.listen(config.PORT, resolve));
  // start subscription server
  subscriptionServer(httpServer);

  console.log(`🚀 Server ready at http://localhost:${config.PORT}${server.graphqlPath}`);
  console.log(`🚀 Subscriptions ready at ws://localhost:${config.PORT}${server.graphqlPath}`);

  return { server, app, httpServer };
}

startApolloServer();
