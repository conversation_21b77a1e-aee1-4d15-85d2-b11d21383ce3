/* eslint-disable no-tabs */
/**
好的，我来总结一下这段代码中每个 resolver 的功能：

**Subscription (GraphQL 订阅):**

*   **`subscribePlaceOrder`**: 订阅新订单。(used by restaurant)
    *   `subscribe`:  使用 `pubsub.asyncIterator(PLACE_ORDER)` 监听 `PLACE_ORDER` 事件。
    *   `withFilter`: 过滤订单，只推送给指定 `restaurant` 的订阅者。

*   **`SubscribeOrderStatus`**: 订阅订单状态的改变。
    *   `subscribe`: 使用 `pubsub.asyncIterator(ORDER_STATUS_CHANGED)` 监听 `ORDER_STATUS_CHANGED` 事件。
    *   `withFilter`: 过滤订单状态变更，只推送给指定 `userId` (用户)的订阅者。

*   **`subscriptionAssignRider`**: 订阅骑手分配事件。
    *   `subscribe`: 使用 `pubsub.asyncIterator(ASSIGN_RIDER)` 监听 `ASSIGN_RIDER` 事件。
    *   `withFilter`: 过滤骑手分配事件，只推送给指定 `riderId` (骑手) 的订阅者。

*   **`subscriptionOrder`**: 订阅订单的更新。
    *   `subscribe`: 使用 `pubsub.asyncIterator(SUBSCRIPTION_ORDER)` 监听 `SUBSCRIPTION_ORDER` 事件。
    *   `withFilter`: 过滤订单更新，只推送给指定 `id` (订单ID) 的订阅者。

**Query (GraphQL 查询):**

*   **`order`**: 根据 ID 获取单个订单。
    *   验证用户已认证 (`req.isAuth`)。
    *   使用 `Order.findById(args.id)` 查找订单。
    *   使用 `transformOrder` 转换订单数据。

*   **`orderPaypal`**: 根据 PayPal ID 获取单个订单
    *   验证用户已认证 (`req.isAuth`)。
    *   使用 `Paypal.findById(args.id)` 查找 PayPal 订单记录。
    *   使用 `transformOrder` 转换订单数据。

*   **`orderStripe`**: 根据 Stripe ID 获取单个订单
    *   验证用户已认证 (`req.isAuth`)。
    *   使用 `Stripe.findById(args.id)` 查找 Stripe 订单记录。
    *   使用 `transformOrder` 转换订单数据。

*   **`orders`**: 获取订单列表，支持过滤和分页。
    *   验证用户已认证 (`req.isAuth`)。
    *   根据 `restaurantId`、`restaurantBrandId`、`orderStatus`、`startTime`、`endTime` 构建查询条件。
    *   使用 `Order.find(query)` 查找订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)。
    *   使用 `transformOrder` 转换每个订单的数据。

*   **`getOrdersByDateRange`**: 根据日期范围获取餐厅的订单。
    *   按餐厅 ID 和日期范围查询订单。
    *   过滤出货到付款 (COD) 且已送达 (DELIVERED) 的订单。
    *   计算货到付款订单的总金额和数量。
    *   返回订单列表、总金额和订单数量。

*   **`ordersByRestId`**: 获取指定餐厅的订单列表，支持搜索和分页。
    *   如果提供了 `search` 参数，则根据订单 ID 进行模糊搜索。
    *   否则，根据 `restaurant` ID 查询订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)。
    *   使用 `transformOrder` 转换每个订单的数据。

*   **`undeliveredOrders`**: 获取用户的未送达订单列表。
    *   验证用户已认证 (`req.isAuth`)。
    *   查询用户 (`req.userId`) 的状态为 `PENDING`、`PICKED` 或 `ACCEPTED` 的订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)。
    *   使用 `transformOrder` 转换每个订单的数据。

*   **`deliveredOrders`**: 获取用户的已送达订单列表。
    *   验证用户已认证 (`req.isAuth`)。
    *   查询用户 (`req.userId`) 的状态为 `DELIVERED` 或 `COMPLETED` 的订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)。
    *   使用 `transformOrder` 转换每个订单的数据。

*   **`allOrders`**: 获取所有订单的列表，支持分页。
    *   查询所有订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)。
    *   使用 `transformOrder` 转换每个订单的数据。

*   **`pageCount`**: 获取指定餐厅的订单分页总数。
    *   统计指定餐厅的订单数量。
    *   计算分页总数 (`orderCount / 10`) 并向上取整。

*   **`orderCount`**: 获取指定餐厅的订单总数。
    *   统计指定餐厅的 `isActive` 为 `true` 的订单数量。

*   **`reviews`**: 获取用户的评价列表。
    *   验证用户已认证 (`req.isAuth`)。
    *   查询用户 (`req.userId`) 的订单，并按照 `createdAt` 降序排序，进行分页 (`skip`, `limit`)，并且 populate `review` 字段。
    *   使用 `transformReviews` 转换评价数据。

*   **`getOrderStatuses`**: 获取订单状态的枚举值。
    *   返回 `order_status` 常量。

*   **`getPaymentStatuses`**: 获取支付状态的枚举值。
    *   返回 `payment_status` 常量。

**Mutation (GraphQL 变更):**

*   **`placeOrder`**: 下单。
    *   (注释掉)验证用户已认证 (`req.isAuth`)。
    *   根据参数创建新的订单，包括验证餐厅、用户、地址，计算价格，创建订单条目等。
    *   生成唯一的订单 ID。
    *   根据支付方式 (`COD`, `PAYPAL`, `STRIPE`) 创建不同的订单记录。
    *   发送通知给用户和餐厅。
    *   发布订单创建事件 (`PLACE_ORDER`)。
    *   使用 `transformOrder` 转换订单数据。

*   **`editOrder`**: 编辑订单。
    *   验证用户已认证 (`req.isAuth`)。
    *   根据参数更新订单信息。
    *   使用 `transformOrder` 转换订单数据。

*   **`updateOrderStatus`**: 更新订单状态。
    *   根据 ID 查找订单。
    *   更新订单状态 (`orderStatus`) 和原因 (`reason`)。
    *   发送通知给用户。
    *   发布订单状态变更事件 (`ORDER_STATUS_CHANGED`)。
    *   使用 `transformOrder` 转换订单数据。

*   **`updatePaymentStatus`**: 更新订单支付状态。
    *   根据 ID 查找订单。
    *   更新支付状态 (`paymentStatus`) 和已支付金额 (`paidAmount`)。
    *   使用 `transformOrder` 转换订单数据。

*   **`muteRing`**: 静音订单响铃。
    *   根据订单 ID 查找订单。
    *   设置 `isRinged` 为 `false`。

*   **`abortOrder`**: 取消订单。
    *   验证用户已认证 (`req.isAuth`)。
    *   根据 ID 查找订单。
    *   设置订单状态为 `CANCELLED`。
    *   发布订单更新事件。
    *   使用 `transformOrder` 转换订单数据。

总的来说，这段代码定义了一系列用于查询和操作订单的 GraphQL resolver，包括订阅订单事件、查询订单信息、下单、编辑订单、更新订单状态和支付状态等功能。

 *
*/

const Customer = require('../../models/customer');
// const Rider = require('../../models/rider')
const Order = require('../../models/order');
const Item = require('../../models/item');
const Coupon = require('../../models/coupon');
const Point = require('../../models/point');
const Zone = require('../../models/zone');
const Restaurant = require('../../models/restaurant');
const Configuration = require('../../models/configuration');
const Paypal = require('../../models/paypal');
const Stripe = require('../../models/stripe');
const logger = require('../../helpers/logger');
const { transformOrder, transformReviews } = require('./merge');
const { payment_status, ORDER_STATUS } = require('../../helpers/enum');
const { sendEmail } = require('../../helpers/email');
const { sendNotification, calculateDistance } = require('../../helpers/utilities');
const { placeOrderTemplate } = require('../../helpers/templates');
const { sendNotificationToRestaurant } = require('../../helpers/notifications');
const { withFilter } = require('graphql-subscriptions');
const {
  pubsub,
  publishToUser,
  publishToDashboard,
  publishOrder,
  publishToDispatcher,
  PLACE_ORDER,
  ORDER_STATUS_CHANGED,
  ASSIGN_RIDER,
  SUBSCRIPTION_ORDER
} = require('../../helpers/pubsub');
const { sendNotificationToUser } = require('../../helpers/notifications');
const { sendNotificationToCustomerWeb } = require('../../helpers/firebase-web-notifications');

var DELIVERY_CHARGES = 0.0;
module.exports = {
  Subscription: {
    subscribePlaceOrder: {
      subscribe: withFilter(
        () => pubsub.asyncIterator(PLACE_ORDER),
        (payload, args, context) => {
          const restaurantId = payload.subscribePlaceOrder.restaurantId;
          console.log('restaurantId', restaurantId);
          return restaurantId === args.restaurant;
        }
      )
    },
    SubscribeOrderStatus: {
      subscribe: withFilter(
        () => pubsub.asyncIterator(ORDER_STATUS_CHANGED),
        (payload, args, context) => {
          const customerId = payload.SubscribeOrderStatus.customerId.toString();
          return customerId === args.customerId;
        }
      )
    },
    subscriptionAssignRider: {
      subscribe: withFilter(
        () => pubsub.asyncIterator(ASSIGN_RIDER),
        (payload, args) => {
          const riderId = payload.subscriptionAssignRider.userId.toString();
          return riderId === args.riderId;
        }
      )
    },
    subscriptionOrder: {
      subscribe: withFilter(
        () => pubsub.asyncIterator(SUBSCRIPTION_ORDER),
        (payload, args) => {
          const orderId = payload.subscriptionOrder._id.toString();
          return orderId === args.id;
        }
      )
    }
  },
  Query: {
    order: async (_, args, { req, res }) => {
      console.log('order');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const order = await Order.findById(args.id);
        if (!order) throw new Error('Order does not exist');
        console.log(order);
        return transformOrder(order);
      } catch (err) {
        throw err;
      }
    },
    orderPaypal: async (_, args, { req, res }) => {
      console.log('orderPaypal');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const paypal = await Paypal.findById(args.id);
        console.log('PAYPAL: ', paypal);
        if (!paypal) throw new Error('Order does not exist');
        return transformOrder(paypal);
      } catch (err) {
        throw err;
      }
    },
    orderStripe: async (_, args, { req, res }) => {
      console.log('orderStripe');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const stripe = await Stripe.findById(args.id);
        console.log('STRIPE: ', stripe);
        if (!stripe) throw new Error('Order does not exist');
        return transformOrder(stripe);
      } catch (err) {
        throw err;
      }
    },
    orders: async (_, { restaurantId, restaurantBrandId, orderStatus, startTime, endTime, offset }, { req }) => {
      console.log('orders');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const query = {};
        if (restaurantId) query.restaurant = restaurantId;
        if (restaurantBrandId) query.restaurantBrandId = restaurantBrandId;
        if (orderStatus) query.orderStatus = orderStatus;
        if (startTime) {
          query.orderDate = { $gte: startTime }; // 使用 $gte (大于等于)
        }
        if (endTime) {
          query.orderDate = { ...query.orderDate, $lte: endTime }; // 使用 $lte (小于等于)
        }

        const orders = await Order.find(query)
          .sort({ createdAt: -1 })
          .skip(offset || 0)
          .limit(50);

        return orders.map(transformOrder);
      } catch (err) {
        throw err;
      }
    },

    getOrdersByDateRange: async (_, args, context) => {
      try {
        const orders = await Order.find({
          restaurant: args.restaurant,
          createdAt: {
            $gte: new Date(args.startingDate),
            $lt: new Date(args.endingDate)
          }
        }).sort({ createdAt: -1 });

        const cashOnDeliveryOrders = orders.filter(
          order => order.paymentMethod === 'COD' && order.orderStatus === 'DELIVERED'
        );

        const totalAmountCashOnDelivery = cashOnDeliveryOrders
          .reduce((total, order) => total + parseFloat(order.orderAmount), 0)
          .toFixed(2);

        const countCashOnDeliveryOrders = cashOnDeliveryOrders.length;

        return {
          orders: orders.map(order => transformOrder(order)),
          totalAmountCashOnDelivery,
          countCashOnDeliveryOrders
        };
      } catch (err) {
        throw err;
      }
    },
    ordersByRestId: async (_, args, context) => {
      console.log('restaurant orders');
      try {
        let orders = [];
        if (args.search) {
          const search = new RegExp(
            // eslint-disable-next-line no-useless-escape
            args.search.replace(/[\\\[\]()+?.*]/g, c => '\\' + c),
            'i'
          );
          orders = await Order.find({
            restaurant: args.restaurant,
            orderId: search
          }).sort({ createdAt: -1 });
          return orders.map(order => {
            return transformOrder(order);
          });
        } else {
          orders = await Order.find({ restaurant: args.restaurant })
            .sort({ createdAt: -1 })
            .skip((args.page || 0) * args.rows)
            .limit(args.rows);
          return orders.map(order => {
            return transformOrder(order);
          });
        }
      } catch (err) {
        throw err;
      }
    },
    undeliveredOrders: async (_, args, { req, res }) => {
      console.log('undeliveredOrders');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const orders = await Order.find({
          customerId: req.customerId,
          $or: [{ orderStatus: 'PENDING' }, { orderStatus: 'PICKED' }, { orderStatus: 'ACCEPTED' }]
        })
          .sort({ createdAt: -1 })
          .skip(args.offset || 0)
          .limit(10);
        return orders.map(order => {
          return transformOrder(order);
        });
      } catch (err) {
        throw err;
      }
    },
    deliveredOrders: async (_, args, { req, res }) => {
      console.log('deliveredOrders');
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        const orders = await Order.find({
          customer: req.customerId,
          $or: [{ orderStatus: 'DELIVERED' }, { orderStatus: 'COMPLETED' }]
        })
          .sort({ createdAt: -1 })
          .skip(args.offset || 0)
          .limit(10);
        return orders.map(order => {
          return transformOrder(order);
        });
      } catch (err) {
        throw err;
      }
    },
    allOrders: async (_, args, context) => {
      try {
        const orders = await Order.find()
          .sort({ createdAt: -1 })
          .skip((args.page || 0) * 10)
          .limit(10);
        return orders.map(order => {
          return transformOrder(order);
        });
      } catch (err) {
        throw err;
      }
    },
    pageCount: async (_, args, context) => {
      try {
        const orderCount = await Order.countDocuments({
          restaurant: args.restaurant
        });
        const pageCount = orderCount / 10;
        return Math.ceil(pageCount);
      } catch (err) {
        throw err;
      }
    },
    orderCount: async (_, args, context) => {
      try {
        const orderCount = await Order.find({
          isActive: true,
          restaurant: args.restautant
        }).countDocuments();
        return orderCount;
      } catch (err) {
        throw err;
      }
    },
    reviews: async (_, args, { req, res }) => {
      console.log('reviews');
      if (!req.isAuth) {
        throw new Error('Unauthenticated');
      }
      try {
        const orders = await Order.find({ user: req.userId })
          .sort({ createdAt: -1 })
          .skip(args.offset || 0)
          .limit(10)
          .populate('review');
        return transformReviews(orders);
      } catch (err) {
        throw err;
      }
    },
    getOrderStatuses: async (_, args, context) => {
      return order_status;
    },
    getPaymentStatuses: async (_, args, context) => {
      return payment_status;
    }
  },
  Mutation: {
    placeOrder: async (_, args, { req, res }) => {
      /*  if (!req.isAuth) {
        throw new Error('Unauthenticated!')
      } */
      try {
        const restaurant = await Restaurant.findById(args.restaurantId);
        if (!restaurant) throw new Error('Restaurant not found');

        const customer = await Customer.findOne({ customerId: args.customerId });
        if (!customer) throw new Error('Customer not found');
        const address = customer.addresses.find(
          addr => addr.addressId.toString() === args.deliveryAddressId.toString()
        );
        if (!address) throw new Error('Address not found');

        const location = new Point({
          type: 'Point',
          coordinates: [address.coordinates.longitude, address.coordinates.latitude]
        });
        /* const checkZone = await Restaurant.findOne({
          _id: args.restaurant,
          deliveryBounds: { $geoIntersects: { $geometry: location } }
        })
        if (!checkZone && args.isPickedUp !== true) {
          throw new Error("Sorry! we can't deliver to your address.")
        }

        const zone = await Zone.findOne({
          isActive: true,
          location: {
            $geoIntersects: { $geometry: restaurant.location }
          }
        })
        if (!zone) {
          throw new Error('Delivery zone not found')
        } */

        const foods = restaurant.categories.map(c => c.foods).flat();
        const availableAddons = restaurant.addons;
        const availableOptions = restaurant.options;
        const ItemsData = args.orderInput.map(item => {
          const food = foods.find(element => element._id.toString() === item.food);
          const variation = food.variations.find(v => v._id.toString() === item.variation);
          const addonList = [];
          item.addons.forEach((data, index) => {
            const selectedOptions = [];
            data.options.forEach((option, inx) => {
              selectedOptions.push(availableOptions.find(op => op._id.toString() === option));
            });
            const adds = availableAddons.find(addon => addon._id.toString() === data._id.toString());

            addonList.push({
              ...adds._doc,
              options: selectedOptions
            });
          });

          // Ensure variation has required fields for orderVariationSchema
          const processedVariation = {
            _id: variation._id,
            title: variation.title,
            price: variation.price,
            discounted: variation.discounted || 0 // Ensure discounted field is always present
          };

          return new Item({
            food: item.food,
            title: food.title,
            description: food.description,
            image: food.image,
            variation: processedVariation,
            addons: addonList,
            quantity: item.quantity,
            specialInstructions: item.specialInstructions
          });
        });

        // get previous orderid from db
        let configuration = await Configuration.findOne();
        if (!configuration) {
          configuration = new Configuration();
          await configuration.save();
        }

        const orderid = restaurant.orderPrefix + '-' + (Number(restaurant.orderId) + 1);
        restaurant.orderId = Number(restaurant.orderId) + 1;
        await restaurant.save();
        const latOrigin = +restaurant.location.coordinates[1];
        const lonOrigin = +restaurant.location.coordinates[0];
        const latDest = +address.latitude;
        const longDest = +address.longitude;
        const distance = calculateDistance(latOrigin, lonOrigin, latDest, longDest);
        const costType = configuration.costType;

        if (costType === 'fixed') {
          DELIVERY_CHARGES = configuration.deliveryRate;
        } else {
          DELIVERY_CHARGES = Math.ceil(distance) * configuration.deliveryRate;
        }
        logger.debug('distance', { distance, DELIVERY_CHARGES });

        let price = 0.0;

        ItemsData.forEach(async item => {
          let itemPrice = item.variation.price;
          if (item.addons && item.addons.length > 0) {
            const addonDetails = [];
            item.addons.forEach(({ options }) => {
              options.forEach(option => {
                itemPrice = itemPrice + option.price;
                addonDetails.push(`${option.title}	${configuration.currencySymbol}${option.price}`);
              });
            });
          }
          price += itemPrice * item.quantity;
          return `${item.quantity} x ${item.title}${item.variation.title ? `(${item.variation.title})` : ''}	${
            configuration.currencySymbol
          }${item.variation.price}`;
        });
        let coupon = null;
        if (args.couponCode) {
          coupon = await Coupon.findOne({ title: args.couponCode });
          if (coupon) {
            price = price - (coupon.discount / 100) * price;
          }
        }
        // 删除重复声明

        // 准备通用的orderObj
        const orderObj = {
          // zone: zone._id,
          orderId: orderid,
          restaurantId: restaurant.id, // 使用restaurant.id而不是args.restaurantId
          restaurantName: restaurant.name,
          restaurantBrand: restaurant.restaurantBrand,
          restaurantBrandId: restaurant.restaurantBrandId,
          customerId: args.customerId,
          customerPhone: customer.phone,
          deliveryAddressId: address.addressId,
          deliveryCoordinates: location,
          items: ItemsData,
          orderStatus: 'PENDING',
          orderDate: args.orderDate,
          paidAmount: 0,
          deliveryCharges: args.deliveryCharges,
          tipping: args.tipping,
          taxationAmount: args.taxationAmount,
          isPickedUp: args.isPickedUp,
          orderAmount: args.orderAmount,
          paymentStatus: payment_status[0],
          coupon: coupon,
          completionTime: new Date(Date.now() + restaurant.deliveryTime * 60 * 1000),
          instructions: args.instructions,
          paymentMethod: args.paymentMethod
        };

        // 验证地址信息是否完整
        if (!address.formattedAddress) {
          throw new Error('Address is missing formattedAddress');
        }

        // 验证金额字段，不使用默认值
        if (typeof orderObj.orderAmount !== 'number' || isNaN(orderObj.orderAmount) || orderObj.orderAmount < 0) {
          throw new Error(`Invalid orderAmount: ${orderObj.orderAmount}`);
        }

        if (
          typeof orderObj.deliveryCharges !== 'number' ||
          isNaN(orderObj.deliveryCharges) ||
          orderObj.deliveryCharges < 0
        ) {
          throw new Error(`Invalid deliveryCharges: ${orderObj.deliveryCharges}`);
        }

        if (
          typeof orderObj.taxationAmount !== 'number' ||
          isNaN(orderObj.taxationAmount) ||
          orderObj.taxationAmount < 0
        ) {
          throw new Error(`Invalid taxationAmount: ${orderObj.taxationAmount}`);
        }

        // 根据支付方式准备不同的模型对象
        let result = null;
        if (args.paymentMethod === 'COD') {
          // 对于Order模型，使用普通字符串地址
          const order = new Order({
            ...orderObj,
            deliveryAddress: address.formattedAddress
          });
          result = await order.save();

          // 处理订单通知和模板
          const placeOrder_template = await placeOrderTemplate([
            result.orderId,
            ItemsData,
            args.isPickedUp ? restaurant.address : address.formattedAddress,
            `${configuration.currencySymbol} ${Number(price).toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.tipping.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.taxationAmount.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.deliveryCharges.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.orderAmount.toFixed(2)}`,
            configuration.currencySymbol
          ]);

          const transformedOrder = await transformOrder(result);
          publishToDashboard(result.restaurantId.toString(), transformedOrder, 'new');
          // 使用restaurantId而非restaurant字段
          sendNotificationToRestaurant(result.restaurantId, result);
        } else if (args.paymentMethod === 'PAYPAL' || args.paymentMethod === 'STRIPE') {
          // 对于Paypal和Stripe模型，使用与Order相同的字段结构
          const paymentOrderObj = {
            ...orderObj,
            restaurant: restaurant._id, // 设置 restaurant 字段为 ObjectId
            restaurantId: restaurant.id, // 确保 restaurantId 是字符串
            deliveryAddress: address.formattedAddress,
            deliveryAddressId: address.addressId,
            deliveryCoordinates: location
          };

          console.log(`Creating ${args.paymentMethod} order with data:`, {
            orderAmount: paymentOrderObj.orderAmount,
            deliveryCharges: paymentOrderObj.deliveryCharges,
            deliveryAddress: paymentOrderObj.deliveryAddress
          });

          // 根据支付方式创建相应的模型
          const PaymentModel = args.paymentMethod === 'PAYPAL' ? Paypal : Stripe;
          const payment = new PaymentModel(paymentOrderObj);
          result = await payment.save();

          if (args.paymentMethod === 'STRIPE') {
            console.log('Stripe result:', result);
          }
        } else {
          throw new Error('Invalid Payment Method');
        }

        // 更新客户的订单历史记录
        try {
          // 创建订单简要信息对象
          const orderBrief = {
            orderId: result._id,
            amount: result.orderAmount,
            restaurantId: result.restaurantId,
            restaurantName: result.restaurantName,
            restaurantBrandId: restaurant.restaurantBrandId,
            restaurantBrand: restaurant.restaurantBrand,
            orderStatus: result.orderStatus,
            orderDate: result.orderDate
          };

          // 将订单添加到客户的订单历史记录中
          customer.orderHistory.unshift(orderBrief); // 添加到数组开头，使最新订单显示在前面

          // 保存客户信息
          await customer.save();

          console.log('Updated customer order history:', {
            customerId: customer.customerId,
            orderHistoryLength: customer.orderHistory.length
          });
        } catch (error) {
          console.error('Error updating customer order history:', error);
          // 不抛出错误，因为这不应该影响订单创建的主要流程
        }
        const orderResult = await transformOrder(result);
        return orderResult;
      } catch (err) {
        throw err;
      }
    },
    editOrder: async (_, args, { req, res }) => {
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      try {
        // Get the existing order to find the restaurant
        const existingOrder = await Order.findOne({ _id: args._id, customerId: req.customerId });
        if (!existingOrder) {
          throw new Error('order does not exist');
        }

        // Get restaurant data to process variations
        const restaurant = await Restaurant.findById(existingOrder.restaurantId);
        if (!restaurant) {
          throw new Error('Restaurant not found');
        }

        const foods = restaurant.categories.map(c => c.foods).flat();
        const availableAddons = restaurant.addons;
        const availableOptions = restaurant.options;

        const items = args.orderInput.map(async function (item) {
          // Find the food and variation to get complete data
          const food = foods.find(element => element._id.toString() === item.food);
          if (!food) {
            throw new Error(`Food not found: ${item.food}`);
          }

          const variation = food.variations.find(v => v._id.toString() === item.variation);
          if (!variation) {
            throw new Error(`Variation not found: ${item.variation}`);
          }

          // Process addons
          const addonList = [];
          if (item.addons && Array.isArray(item.addons)) {
            item.addons.forEach((data) => {
              const selectedOptions = [];
              if (data.options && Array.isArray(data.options)) {
                data.options.forEach((option) => {
                  selectedOptions.push(availableOptions.find(op => op._id.toString() === option));
                });
              }
              const addon = availableAddons.find(a => a._id.toString() === data._id.toString());
              if (addon) {
                addonList.push({
                  ...(addon._doc || addon),
                  options: selectedOptions
                });
              }
            });
          }

          // Ensure variation has required fields for orderVariationSchema
          const processedVariation = {
            _id: variation._id,
            title: variation.title,
            price: variation.price,
            discounted: variation.discounted || 0 // Ensure discounted field is always present
          };

          const newItem = new Item({
            food: item.food,
            title: food.title,
            description: food.description,
            image: food.image,
            variation: processedVariation,
            addons: addonList,
            quantity: item.quantity,
            specialInstructions: item.specialInstructions
          });
          const result = await newItem.save();
          return result._id;
        });
        const completed = await Promise.all(items);
        existingOrder.items = completed;
        const result = await existingOrder.save();
        return transformOrder(result);
      } catch (err) {
        throw err;
      }
    },
    updateOrderStatus: async (_, args, context) => {
      console.log('updateOrderStatus');
      try {
        const order = await Order.findById(args.id);
        const restaurant = await Restaurant.findById(order.restaurantId);
        if (args.status === 'ACCEPTED') {
          order.completionTime = new Date(Date.now() + restaurant.deliveryTime * 60 * 1000);
        }
        order.orderStatus = args.status;
        order.reason = args.reason;
        const result = await order.save();

        const transformedOrder = await transformOrder(result);
        const customer = await Customer.findOne({ customerId: order.customerId });

        // 更新客户的订单历史记录中的订单状态
        if (customer) {
          try {
            // 查找客户订单历史中的对应订单
            const orderIndex = customer.orderHistory.findIndex(o => o.orderId.toString() === order._id.toString());

            if (orderIndex !== -1) {
              // 更新订单状态
              customer.orderHistory[orderIndex].orderStatus = order.orderStatus;
              await customer.save();

              console.log('Updated order status in customer history:', {
                customerId: customer.customerId,
                orderId: order._id,
                newStatus: order.orderStatus
              });
            } else {
              console.warn('Order not found in customer history:', {
                customerId: customer.customerId,
                orderId: order._id
              });
            }
          } catch (error) {
            console.error('Error updating order status in customer history:', error);
            // 不抛出错误，因为这不应该影响订单状态更新的主要流程
          }
        }

        /*
        publishToUser(result.user.toString(), transformedOrder, 'update')
        publishOrder(transformedOrder)
        sendNotificationToUser(result.user, result)
        sendNotificationToCustomerWeb(
          user.notificationTokenWeb,
          `Order status: ${result.orderStatus}`,
          `Order ID ${result.orderId}`
        )
        */
        return transformOrder(result);
      } catch (err) {
        throw err;
      }
    },
    updatePaymentStatus: async (_, args, context) => {
      console.log('updatePaymentStatus', args.id, args.status);
      try {
        const order = await Order.findById(args.id);
        if (!order) throw new Error('Order does not exist');
        order.paymentStatus = args.status;
        order.paidAmount = args.status === 'PAID' ? order.orderAmount : 0.0;
        const result = await order.save();

        // 更新客户的订单历史记录中的支付状态
        try {
          const customer = await Customer.findOne({ customerId: order.customerId });
          if (customer) {
            // 查找客户订单历史中的对应订单
            const orderIndex = customer.orderHistory.findIndex(o => o.orderId.toString() === order._id.toString());

            if (orderIndex !== -1) {
              // 更新订单支付状态
              // 注意：由于 OrderBrief 模型中没有 paymentStatus 字段，
              // 我们只能更新订单状态，但不能直接更新支付状态
              if (args.status === 'PAID') {
                // 如果支付完成，将订单状态更新为 ACCEPTED
                customer.orderHistory[orderIndex].orderStatus = 'ACCEPTED';
                await customer.save();

                console.log('Updated order status in customer history after payment:', {
                  customerId: customer.customerId,
                  orderId: order._id,
                  paymentStatus: args.status,
                  newOrderStatus: 'ACCEPTED'
                });
              }
            } else {
              console.warn('Order not found in customer history for payment update:', {
                customerId: customer.customerId,
                orderId: order._id
              });
            }
          }
        } catch (error) {
          console.error('Error updating payment status in customer history:', error);
          // 不抛出错误，因为这不应该影响支付状态更新的主要流程
        }

        return transformOrder(result);
      } catch (error) {
        throw error;
      }
    },
    muteRing: async (_, args, { req }) => {
      try {
        const order = await Order.findOne({ orderId: args.orderId });
        if (!order) throw new Error('Order does not exist');
        order.isRinged = false;
        await order.save();
        return true;
      } catch (error) {
        throw error;
      }
    },
    abortOrder: async (_, args, { req }) => {
      console.log('abortOrder', args);
      if (!req.isAuth) {
        throw new Error('Unauthenticated!');
      }
      const order = await Order.findById(args.id);
      order.orderStatus = ORDER_STATUS.CANCELLED;
      const result = await order.save();

      const transformedOrder = await transformOrder(result);
      publishOrder(transformedOrder);

      return transformedOrder;
    },

    placeOrderWhatsApp: async (_, args, { req, res }) => {
      /*  if (!req.isAuth) {
        throw new Error('Unauthenticated!')
      } */
      try {
        const restaurant = await Restaurant.findById(args.restaurantId);
        if (!restaurant) throw new Error('Restaurant not found');

        const customer = await Customer.findOne({ customerId: args.customerId });
        if (!customer) throw new Error('Customer not found');

        // Only validate delivery address for non-pickup orders
        let address = null;
        let location = null;

        if (!args.isPickedUp) {
          // For delivery orders, validate the address
          address = customer.addresses.find(
            addr => addr.addressId.toString() === args.deliveryAddressId.toString()
          );
          if (!address) throw new Error('Address not found');

          // Create location point for delivery orders
          location = new Point({
            type: 'Point',
            coordinates: [address.coordinates.longitude, address.coordinates.latitude]
          });
        }

        const foods = restaurant.categories.map(c => c.foods).flat();
        const availableAddons = restaurant.addons;
        const availableOptions = restaurant.options;
        const ItemsData = args.orderInput.map(item => {
          const food = foods.find(element => element._id.toString() === item.food);
          const variation = food.variations.find(v => v._id.toString() === item.variation);
          const addonList = [];
          item.addons.forEach((data, index) => {
            const selectedOptions = [];
            data.options.forEach((option, inx) => {
              selectedOptions.push(availableOptions.find(op => op._id.toString() === option));
            });
            const adds = availableAddons.find(addon => addon._id.toString() === data._id.toString());

            addonList.push({
              ...adds._doc,
              options: selectedOptions
            });
          });

          // Ensure variation has required fields for orderVariationSchema
          const processedVariation = {
            _id: variation._id,
            title: variation.title,
            price: variation.price,
            discounted: variation.discounted || 0 // Ensure discounted field is always present
          };

          return new Item({
            food: item.food,
            title: food.title,
            description: food.description,
            image: food.image,
            variation: processedVariation,
            addons: addonList,
            quantity: item.quantity,
            specialInstructions: item.specialInstructions
          });
        });

        // get previous orderid from db
        let configuration = await Configuration.findOne();
        if (!configuration) {
          configuration = new Configuration();
          await configuration.save();
        }

        const orderid = restaurant.orderPrefix + '-' + (Number(restaurant.orderId) + 1);
        restaurant.orderId = Number(restaurant.orderId) + 1;
        await restaurant.save();

        // 计算配送费
        let deliveryCharges = 0.0;

        // 如果是自取订单，配送费直接为0
        if (args.isPickedUp) {
          deliveryCharges = 0.0;
          logger.debug('Pickup order, deliveryCharges set to 0');
        }
        // 检查是否已提供配送费，如果大于0则直接使用，否则计算配送费
        else if (args.deliveryCharges > 0) {
          deliveryCharges = args.deliveryCharges;
          logger.debug('Using provided deliveryCharges', { deliveryCharges });
        } else {
          // 计算餐厅和配送地址之间的距离
          const latOrigin = +restaurant.location.coordinates[1];
          const lonOrigin = +restaurant.location.coordinates[0];
          const latDest = +address.coordinates.latitude;
          const longDest = +address.coordinates.longitude;
          const distance = calculateDistance(latOrigin, lonOrigin, latDest, longDest);

          // 根据餐厅配置计算配送费
          if (restaurant.deliveryCostType === 'fixed') {
            // 固定费用模式
            deliveryCharges = restaurant.deliveryCostMin;
          } else {
            // 基于距离的费用模式
            const baseCost = restaurant.deliveryCostRate * distance;
            const minCost = restaurant.deliveryCostMin;
            // 取基于距离的费用和最低费用中的较大值
            deliveryCharges = Math.max(baseCost, minCost);
          }
          logger.debug('Calculated deliveryCharges', {
            distance,
            deliveryCharges,
            deliveryCostType: restaurant.deliveryCostType
          });
        }

        // 保留原来的计算逻辑，并与传入的参数进行比较
        let calculatedPrice = 0.0;

        ItemsData.forEach(async item => {
          let itemPrice = item.variation.price;
          if (item.addons && item.addons.length > 0) {
            const addonDetails = [];
            item.addons.forEach(({ options }) => {
              options.forEach(option => {
                itemPrice = itemPrice + option.price;
                addonDetails.push(`${option.title}	${configuration.currencySymbol}${option.price}`);
              });
            });
          }
          calculatedPrice += itemPrice * item.quantity;
          return `${item.quantity} x ${item.title}${item.variation.title ? `(${item.variation.title})` : ''}	${
            configuration.currencySymbol
          }${item.variation.price}`;
        });

        // 比较计算结果与传入的 itemsSubtotal
        if (Math.abs(calculatedPrice - args.itemsSubtotal) > 0.01) {
          logger.warn('前端商品小计与服务端计算不匹配，使用服务端计算金额', {
            calculated: calculatedPrice,
            provided: args.itemsSubtotal,
            difference: calculatedPrice - args.itemsSubtotal,
            orderId: args.orderId
          });
        }

        // 使用服务端计算的结果，确保金额准确性
        let price = calculatedPrice;

        // 计算订单总金额
        let calculatedOrderAmount = price + deliveryCharges + args.taxationAmount + args.tipping;

        // 处理优惠券
        let coupon = null;
        if (args.couponCode) {
          coupon = await Coupon.findOne({ title: args.couponCode });
          if (coupon) {
            // 应用优惠券折扣到商品小计
            price = price - (coupon.discount / 100) * price;
            // 重新计算订单总金额
            calculatedOrderAmount = price + deliveryCharges + args.taxationAmount + args.tipping;
          }
        }

        // 比较计算的订单总金额与传入的 orderAmount
        if (Math.abs(calculatedOrderAmount - args.orderAmount) > 0.01) {
          logger.warn('前端订单总额与服务端计算不匹配，使用服务端计算金额', {
            calculated: calculatedOrderAmount,
            provided: args.orderAmount,
            difference: calculatedOrderAmount - args.orderAmount,
            itemsSubtotal: calculatedPrice,
            deliveryCharges,
            taxationAmount: args.taxationAmount,
            tipping: args.tipping,
            orderId: args.orderId
          });
        }
        // 优惠券已在上面处理

        // 准备通用的orderObj
        const orderObj = {
          // zone: zone._id,
          orderId: orderid,
          restaurantId: restaurant.id, // 使用restaurant.id而不是args.restaurantId
          restaurantName: restaurant.name,
          restaurantBrand: restaurant.restaurantBrand,
          restaurantBrandId: restaurant.restaurantBrandId, // 生成新的ObjectId作为后备
          customerId: args.customerId,
          customerPhone: customer.phone,
          items: ItemsData,
          orderStatus: 'PENDING',
          orderDate: args.orderDate,
          paidAmount: 0,
          deliveryCharges,
          tipping: args.tipping,
          taxationAmount: args.taxationAmount,
          isPickedUp: args.isPickedUp,
          orderAmount: calculatedOrderAmount, // 使用计算的订单总金额
          paymentStatus: payment_status[0],
          coupon: coupon,
          completionTime: new Date(Date.now() + restaurant.deliveryTime * 60 * 1000),
          instructions: args.instructions,
          paymentMethod: args.paymentMethod
        };

        // 只有非自取订单才需要设置配送地址相关字段
        if (!args.isPickedUp) {
          orderObj.deliveryAddressId = address.addressId;
          orderObj.deliveryCoordinates = location;

          // 验证地址信息是否完整
          if (!address.formattedAddress) {
            throw new Error('Address is missing formattedAddress');
          }
        } else {
          // 自取订单使用餐厅地址作为配送地址
          orderObj.deliveryAddressId = 'pickup';
        }

        // 验证金额字段，不使用默认值
        if (typeof orderObj.orderAmount !== 'number' || isNaN(orderObj.orderAmount) || orderObj.orderAmount < 0) {
          throw new Error(`Invalid orderAmount: ${orderObj.orderAmount}`);
        }

        if (
          typeof orderObj.deliveryCharges !== 'number' ||
          isNaN(orderObj.deliveryCharges) ||
          orderObj.deliveryCharges < 0
        ) {
          throw new Error(`Invalid deliveryCharges: ${orderObj.deliveryCharges}`);
        }

        if (
          typeof orderObj.taxationAmount !== 'number' ||
          isNaN(orderObj.taxationAmount) ||
          orderObj.taxationAmount < 0
        ) {
          throw new Error(`Invalid taxationAmount: ${orderObj.taxationAmount}`);
        }

        // 根据支付方式准备不同的模型对象
        let result = null;
        if (args.paymentMethod === 'COD') {
          // 对于Order模型，使用普通字符串地址
          const order = new Order({
            ...orderObj,
            deliveryAddress: args.isPickedUp ? restaurant.address : address.formattedAddress
          });
          result = await order.save();

          // 处理订单通知和模板
          const placeOrder_template = await placeOrderTemplate([
            result.orderId,
            ItemsData,
            args.isPickedUp ? restaurant.address : address.formattedAddress,
            `${configuration.currencySymbol} ${Number(price).toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.tipping.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.taxationAmount.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.deliveryCharges.toFixed(2)}`,
            `${configuration.currencySymbol} ${orderObj.orderAmount.toFixed(2)}`,
            configuration.currencySymbol
          ]);

          const transformedOrder = await transformOrder(result);
          publishToDashboard(result.restaurantId.toString(), transformedOrder, 'new');
          // 使用restaurantId而非restaurant字段
          sendNotificationToRestaurant(result.restaurantId, result);
        } else if (args.paymentMethod === 'PAYPAL' || args.paymentMethod === 'STRIPE') {
          // 对于Paypal和Stripe模型，使用与Order相同的字段结构
          let paymentOrderObj = {
            ...orderObj,
            restaurant: restaurant._id, // 设置 restaurant 字段为 ObjectId
            restaurantId: restaurant.id, // 确保 restaurantId 是字符串
            user: customer._id // 设置 user 字段为 Customer 的 _id
          };

          // 根据是否自取设置不同的地址信息
          if (args.isPickedUp) {
            // 自取订单使用餐厅地址
            paymentOrderObj = {
              ...paymentOrderObj,
              deliveryAddress: restaurant.address,
              deliveryAddressId: 'pickup'
            };
          } else if (address) {
            // 配送订单使用客户地址
            paymentOrderObj = {
              ...paymentOrderObj,
              deliveryAddress: address.formattedAddress,
              deliveryAddressId: address.addressId,
              deliveryCoordinates: location
            };
          }

          logger.debug(`Creating ${args.paymentMethod} order with data:`, {
            orderAmount: paymentOrderObj.orderAmount,
            deliveryCharges: paymentOrderObj.deliveryCharges,
            isPickedUp: args.isPickedUp
          });

          // 根据支付方式创建相应的模型
          const PaymentModel = args.paymentMethod === 'PAYPAL' ? Paypal : Stripe;
          const payment = new PaymentModel(paymentOrderObj);
          result = await payment.save();

          if (args.paymentMethod === 'STRIPE') {
            logger.debug('Stripe result:', result);
          }
        } else {
          throw new Error('Invalid Payment Method');
        }

        // 更新客户的订单历史记录
        try {
          // 创建订单简要信息对象
          const orderBrief = {
            orderId: result._id,
            amount: result.orderAmount,
            restaurantId: result.restaurantId,
            restaurantName: result.restaurantName,
            restaurantBrandId: restaurant.restaurantBrandId,
            restaurantBrand: restaurant.restaurantBrand,
            orderStatus: result.orderStatus,
            orderDate: result.orderDate
          };

          // 将订单添加到客户的订单历史记录中
          customer.orderHistory.unshift(orderBrief); // 添加到数组开头，使最新订单显示在前面

          // 保存客户信息
          await customer.save();

          logger.debug('Updated customer order history:', {
            customerId: customer.customerId,
            orderHistoryLength: customer.orderHistory.length
          });
        } catch (error) {
          logger.error('Error updating customer order history:', error);
          // 不抛出错误，因为这不应该影响订单创建的主要流程
        }
        const orderResult = await transformOrder(result);
        return orderResult;
      } catch (err) {
        throw err;
      }
    }
  }
};
