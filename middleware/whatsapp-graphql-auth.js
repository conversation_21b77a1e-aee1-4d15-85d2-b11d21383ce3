/**
 * WhatsApp GraphQL认证中间件
 * 处理带有X-WhatsAppW-Token的GraphQL请求
 * 认证成功后设置userId和userType，使WhatsApp用户能通过基础权限检查
 */
const { GraphQLError } = require('graphql');
const sessionService = require('../whatsapp/services/sessionService');
const SessionIdGenerator = require('../whatsapp/utils/sessionIdGenerator');
const logger = require('../helpers/logger');

// 定义错误类型常量
const ERROR_TYPES = {
  INVALID_TOKEN: 'INVALID_TOKEN',
  EXPIRED_SESSION: 'EXPIRED_SESSION',
  MISSING_TOKEN: 'MISSING_TOKEN',
  INVALID_CUSTOMER: 'INVALID_CUSTOMER'
};

// 错误类型到错误信息的映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.INVALID_TOKEN]: 'Invalid token. Please send a message in WhatsApp to get a new link.',
  [ERROR_TYPES.EXPIRED_SESSION]: 'Your session has expired. Please send a message in WhatsApp to get a new link.',
  [ERROR_TYPES.MISSING_TOKEN]: 'Authentication required. Please send a message in WhatsApp to get a new link.',
  [ERROR_TYPES.INVALID_CUSTOMER]: 'Customer not found. Please send a message in WhatsApp to get a new link.'
};

// 错误类型到错误代码的映射
const ERROR_CODES = {
  [ERROR_TYPES.INVALID_TOKEN]: 'WHATSAPP_INVALID_TOKEN',
  [ERROR_TYPES.EXPIRED_SESSION]: 'WHATSAPP_EXPIRED_SESSION',
  [ERROR_TYPES.MISSING_TOKEN]: 'WHATSAPP_MISSING_TOKEN',
  [ERROR_TYPES.INVALID_CUSTOMER]: 'WHATSAPP_INVALID_CUSTOMER'
};

/**
 * 创建WhatsApp GraphQL错误
 * @param {string} errorType - 错误类型，必须是ERROR_TYPES中的一个值
 * @returns {GraphQLError} GraphQL错误对象
 */
const createWhatsAppError = errorType => {
  // 验证错误类型
  if (!ERROR_TYPES[errorType]) {
    logger.warn(`Unknown error type: ${errorType}, using EXPIRED_SESSION as fallback`);
    errorType = ERROR_TYPES.EXPIRED_SESSION;
  }

  return new GraphQLError(ERROR_MESSAGES[errorType], {
    extensions: {
      code: ERROR_CODES[errorType],
      whatsAppMessage: ERROR_MESSAGES[errorType]
    }
  });
};

/**
 * 处理WhatsApp GraphQL认证
 * @param {Object} context - GraphQL上下文
 * @returns {Promise<Object>} 更新后的上下文
 */
async function processWhatsAppAuth(context) {
  const { req } = context;

  // 检查是否存在WhatsApp专用请求头
  const whatsappToken = req.headers['x-whatsappw-token'];

  if (!whatsappToken) {
    // 如果没有token，返回原始上下文
    return context;
  }

  try {
    // 验证token格式
    if (!SessionIdGenerator.validateToken(whatsappToken)) {
      logger.debug(`Invalid WhatsApp token format: ${whatsappToken}`);
      return {
        ...context,
        whatsAppAuth: true,
        whatsAppErrorType: ERROR_TYPES.INVALID_TOKEN
      };
    }

    // 获取session
    const session = await sessionService.getSessionByToken(whatsappToken);

    if (!session) {
      logger.debug(`No session found for WhatsApp token: ${whatsappToken}`);
      return {
        ...context,
        whatsAppAuth: true,
        whatsAppErrorType: ERROR_TYPES.EXPIRED_SESSION
      };
    }

    // 从session中获取customerId
    const customerId = session.context?.customer?.customerId;

    if (!customerId) {
      logger.debug(`No customerId found in session for token: ${whatsappToken}`);
      return {
        ...context,
        whatsAppAuth: true,
        whatsAppErrorType: ERROR_TYPES.INVALID_CUSTOMER
      };
    }

    // 认证成功，将customerId添加到上下文
    logger.debug(`Authenticated Web WhatsApp request with customerId: ${customerId}`);
    return {
      ...context,
      req: {
        ...context.req,
        userId: customerId,        // 将customerId设为userId
        userType: 'default'        // 设置为customer用户类型（对应USER_ROLES.CUSTOMER）
      },
      whatsAppAuth: true,
      whatsAppCustomerId: customerId,
      whatsAppSession: session,
      authType: 'WEB_WHATSAPP'  // 标识这是Web WhatsApp认证
    };
  } catch (error) {
    logger.error('Error processing WhatsApp token:', error);
    return {
      ...context,
      whatsAppAuth: true,
      whatsAppErrorType: ERROR_TYPES.INVALID_TOKEN
    };
  }
}

module.exports = {
  processWhatsAppAuth,
  createWhatsAppError,
  ERROR_TYPES
};
